/**
 * Save layout composable
 * Handles saving widget layout configurations
 */
export function useSaveLayout(arrangedWidgets, preferenceStore, close) {
  const saveLayout = () => {
    if (arrangedWidgets.value.length === 0) {
      ElMessage.warning('No widgets to save')
      return
    }

    arrangedWidgets.value.forEach((widget) => {
      const config = {
        '--window-width': Math.round(widget.realWidth).toString(),
        '--window-height': Math.round(widget.realHeight).toString(),
        '--window-x': Math.round(widget.realX).toString(),
        '--window-y': Math.round(widget.realY).toString(),
      }

      const scrcpy = window.appStore.get('scrcpy')

      if (widget.type === 'global') {
        const globalConfig = { ...scrcpy.global || {}, ...config }
        window.appStore.set('scrcpy.global', globalConfig)
      }
      else {
        const deviceConfig = { ...scrcpy[widget.deviceId] || {}, ...config }
        window.appStore.set(['scrcpy', widget.deviceId], deviceConfig)
      }
    })

    preferenceStore.init()
    ElMessage.success(`Layout saved successfully for ${arrangedWidgets.value.length} widget(s)`)
    close()
  }

  return {
    saveLayout,
  }
}
