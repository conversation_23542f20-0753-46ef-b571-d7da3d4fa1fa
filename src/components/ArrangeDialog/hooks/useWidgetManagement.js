/**
 * Widget management composable
 * Handles adding, removing, and managing widgets
 */
export function useWidgetManagement(arrangedWidgets, allDevices, hasGlobalWidget, createWidgetFromConfig, preferenceStore) {
  // Helper function to update window.appStore with current widget positions
  const updateAppStore = () => {
    arrangedWidgets.value.forEach((widget) => {
      const config = {
        '--window-width': Math.round(widget.realWidth).toString(),
        '--window-height': Math.round(widget.realHeight).toString(),
        '--window-x': Math.round(widget.realX).toString(),
        '--window-y': Math.round(widget.realY).toString(),
      }

      const scrcpy = window.appStore.get('scrcpy')

      if (widget.type === 'global') {
        const globalConfig = { ...scrcpy.global || {}, ...config }
        window.appStore.set('scrcpy.global', globalConfig)
      }
      else {
        const deviceConfig = { ...scrcpy[widget.deviceId] || {}, ...config }
        window.appStore.set(['scrcpy', widget.deviceId], deviceConfig)
      }
    })

    preferenceStore.init()
  }
  const addWidget = (command) => {
    if (command === 'global') {
      if (hasGlobalWidget.value) {
        ElMessage.warning('Global widget already exists')
        return
      }

      const globalConfig = window.appStore.get('scrcpy.global') || {}
      const config = {
        '--window-width': globalConfig['--window-width'] || '300',
        '--window-height': globalConfig['--window-height'] || '600',
        '--window-x': globalConfig['--window-x'] || (arrangedWidgets.value.length * 50).toString(),
        '--window-y': globalConfig['--window-y'] || (arrangedWidgets.value.length * 50).toString(),
      }

      const widget = createWidgetFromConfig(config, {
        id: 'global',
        type: 'global',
        name: 'Global',
      })
      arrangedWidgets.value.push(widget)
    }
    else {
      const device = allDevices.value.find(d => d.id === command)
      if (!device) {
        ElMessage.error('Device not found')
        return
      }

      const deviceConfig = window.appStore.get(`scrcpy.${command}`) || {}
      const globalConfig = window.appStore.get('scrcpy.global') || {}
      const config = {
        ...globalConfig,
        ...deviceConfig,
        '--window-width': deviceConfig['--window-width'] || globalConfig['--window-width'] || '300',
        '--window-height': deviceConfig['--window-height'] || globalConfig['--window-height'] || '600',
        '--window-x': deviceConfig['--window-x'] || globalConfig['--window-x'] || (arrangedWidgets.value.length * 50).toString(),
        '--window-y': deviceConfig['--window-y'] || globalConfig['--window-y'] || (arrangedWidgets.value.length * 50).toString(),
      }

      const widget = createWidgetFromConfig(config, {
        id: device.id,
        type: 'device',
        deviceId: device.id,
        name: device.name || device.model?.split(':')[1] || device.id,
      })
      arrangedWidgets.value.push(widget)
    }
  }

  const removeWidget = (widgetId) => {
    const index = arrangedWidgets.value.findIndex(w => w.id === widgetId)
    if (index > -1) {
      arrangedWidgets.value.splice(index, 1)
      // Update window.appStore with remaining widgets' positions
      updateAppStore()
    }
  }

  const clearAllWidgets = () => {
    ElMessageBox.confirm(
      'Are you sure you want to clear all widgets?',
      'Confirm',
      {
        confirmButtonText: 'Confirm',
        cancelButtonText: 'Cancel',
        type: 'warning',
      },
    ).then(() => {
      arrangedWidgets.value = []
      // Update window.appStore after clearing all widgets
      updateAppStore()
      ElMessage.success('All widgets cleared')
    }).catch(() => {})
  }

  return {
    addWidget,
    removeWidget,
    clearAllWidgets,
  }
}
